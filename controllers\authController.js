const bcrypt = require('bcryptjs');
const { executeQuery } = require('../config/database');
const { generateToken, formatResponse, sanitizeUser } = require('../utils/helpers');

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res, next) => {
  try {
    const { email, password, first_name, last_name, phone } = req.body;

    // Check if user already exists
    const existingUser = await executeQuery(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser.length > 0) {
      return res.status(400).json(
        formatResponse(false, 'User already exists with this email')
      );
    }

    // Hash password
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const result = await executeQuery(
      `INSERT INTO users (email, password, first_name, last_name, phone) 
       VALUES (?, ?, ?, ?, ?)`,
      [email, hashedPassword, first_name, last_name, phone || null]
    );

    // Get created user
    const user = await executeQuery(
      'SELECT id, email, first_name, last_name, phone, role, is_verified, created_at FROM users WHERE id = ?',
      [result.insertId]
    );

    // Generate token
    const token = generateToken(user[0].id);

    res.status(201).json(
      formatResponse(true, 'User registered successfully', {
        user: user[0],
        token
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Check if user exists
    const users = await executeQuery(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );

    if (users.length === 0) {
      return res.status(401).json(
        formatResponse(false, 'Invalid credentials')
      );
    }

    const user = users[0];

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json(
        formatResponse(false, 'Invalid credentials')
      );
    }

    // Generate token
    const token = generateToken(user.id);

    res.status(200).json(
      formatResponse(true, 'Login successful', {
        user: sanitizeUser(user),
        token
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get current user
// @route   GET /api/auth/me
// @access  Private
const getMe = async (req, res, next) => {
  try {
    const user = await executeQuery(
      'SELECT id, email, first_name, last_name, phone, role, is_verified, created_at FROM users WHERE id = ?',
      [req.user.id]
    );

    res.status(200).json(
      formatResponse(true, 'User profile retrieved', user[0])
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
const updateProfile = async (req, res, next) => {
  try {
    const { first_name, last_name, phone } = req.body;
    const userId = req.user.id;

    // Update user
    await executeQuery(
      'UPDATE users SET first_name = ?, last_name = ?, phone = ? WHERE id = ?',
      [first_name, last_name, phone || null, userId]
    );

    // Get updated user
    const user = await executeQuery(
      'SELECT id, email, first_name, last_name, phone, role, is_verified, created_at FROM users WHERE id = ?',
      [userId]
    );

    res.status(200).json(
      formatResponse(true, 'Profile updated successfully', user[0])
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Change password
// @route   PUT /api/auth/change-password
// @access  Private
const changePassword = async (req, res, next) => {
  try {
    const { current_password, new_password } = req.body;
    const userId = req.user.id;

    // Get current user with password
    const users = await executeQuery(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    );

    const user = users[0];

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(current_password, user.password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json(
        formatResponse(false, 'Current password is incorrect')
      );
    }

    // Hash new password
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
    const hashedNewPassword = await bcrypt.hash(new_password, salt);

    // Update password
    await executeQuery(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedNewPassword, userId]
    );

    res.status(200).json(
      formatResponse(true, 'Password changed successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Logout user (client-side token removal)
// @route   POST /api/auth/logout
// @access  Private
const logout = async (req, res, next) => {
  try {
    res.status(200).json(
      formatResponse(true, 'Logged out successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  register,
  login,
  getMe,
  updateProfile,
  changePassword,
  logout
};
