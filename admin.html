<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Luxe Fashion</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Luxe Fashion Admin Dashboard - Manage your e-commerce store">
    <meta name="keywords" content="admin, dashboard, e-commerce, fashion, management">
    <meta name="author" content="Luxe Fashion">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Admin Dashboard - Luxe Fashion">
    <meta property="og:description" content="Luxe Fashion Admin Dashboard - Manage your e-commerce store">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://luxefashion.com/admin">
    
    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Admin Dashboard - Luxe Fashion">
    <meta name="twitter:description" content="Luxe Fashion Admin Dashboard - Manage your e-commerce store">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- External CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
    <script src="api-config.js"></script>
    
    <style>
        .admin-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .admin-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card.users {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-card.products {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .stat-card.orders {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .stat-card.revenue {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .sidebar-link {
            transition: all 0.3s ease;
        }
        
        .sidebar-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        .sidebar-link.active {
            background: rgba(255, 255, 255, 0.2);
            border-right: 4px solid white;
        }
    </style>
</head>
<body class="bg-gray-50 font-inter">
    <!-- Admin Authentication Check -->
    <div id="auth-check" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <i class="fas fa-shield-alt text-4xl text-primary mb-4"></i>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">Admin Access Required</h2>
                <p class="text-gray-600 mb-6">Please log in with your admin credentials to access the dashboard.</p>
                
                <form id="admin-login-form" class="space-y-4">
                    <div>
                        <input type="email" id="admin-email" placeholder="Admin Email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <input type="password" id="admin-password" placeholder="Password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <button type="submit" class="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-accent transition-colors duration-300">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login to Dashboard
                    </button>
                </form>
                
                <div class="mt-4 text-sm text-gray-500">
                    <p>Demo Credentials:</p>
                    <p>Email: <EMAIL></p>
                    <p>Password: admin123</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Admin Dashboard -->
    <div id="admin-dashboard" class="hidden min-h-screen flex">
        <!-- Sidebar -->
        <div class="admin-sidebar w-64 min-h-screen text-white">
            <div class="p-6">
                <div class="flex items-center space-x-3 mb-8">
                    <i class="fas fa-crown text-2xl text-yellow-300"></i>
                    <div>
                        <h1 class="text-xl font-bold">Luxe Fashion</h1>
                        <p class="text-sm opacity-75">Admin Dashboard</p>
                    </div>
                </div>
                
                <nav class="space-y-2">
                    <a href="#" class="sidebar-link active flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="dashboard">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="products">
                        <i class="fas fa-box"></i>
                        <span>Products</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="orders">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Orders</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="users">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                    <a href="#" class="sidebar-link flex items-center space-x-3 px-4 py-3 rounded-lg" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </nav>
            </div>
            
            <div class="absolute bottom-0 w-64 p-6 border-t border-white border-opacity-20">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-sm"></i>
                    </div>
                    <div>
                        <p class="font-medium" id="admin-name">Admin User</p>
                        <p class="text-sm opacity-75" id="admin-role">Administrator</p>
                    </div>
                </div>
                <button id="admin-logout" class="w-full bg-red-500 hover:bg-red-600 text-white py-2 rounded-lg transition-colors duration-300">
                    <i class="fas fa-sign-out-alt mr-2"></i>
                    Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 id="page-title" class="text-2xl font-bold text-gray-800">Dashboard</h2>
                        <p id="page-subtitle" class="text-gray-600">Welcome to your admin dashboard</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="relative p-2 text-gray-600 hover:text-gray-800">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                        </button>
                        <a href="/" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-accent transition-colors duration-300">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            View Site
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content Area -->
            <main class="p-6 overflow-y-auto" style="height: calc(100vh - 80px);">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="section">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card users rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Users</p>
                                    <p id="total-users" class="text-3xl font-bold">0</p>
                                </div>
                                <i class="fas fa-users text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                        
                        <div class="stat-card products rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Products</p>
                                    <p id="total-products" class="text-3xl font-bold">0</p>
                                </div>
                                <i class="fas fa-box text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                        
                        <div class="stat-card orders rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Orders</p>
                                    <p id="total-orders" class="text-3xl font-bold">0</p>
                                </div>
                                <i class="fas fa-shopping-cart text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                        
                        <div class="stat-card revenue rounded-xl p-6 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Revenue</p>
                                    <p id="total-revenue" class="text-3xl font-bold">$0</p>
                                </div>
                                <i class="fas fa-dollar-sign text-3xl text-white text-opacity-60"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Monthly Sales</h3>
                            <canvas id="salesChart" width="400" height="200"></canvas>
                        </div>
                        
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">User Registrations</h3>
                            <canvas id="usersChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Orders</h3>
                            <div id="recent-orders" class="space-y-3">
                                <!-- Recent orders will be loaded here -->
                            </div>
                        </div>
                        
                        <div class="admin-card rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">Popular Products</h3>
                            <div id="popular-products" class="space-y-3">
                                <!-- Popular products will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections will be added here -->
                <div id="products-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Product Management</h3>
                        <p class="text-gray-600">Product management features coming soon...</p>
                    </div>
                </div>

                <div id="orders-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Order Management</h3>
                        <p class="text-gray-600">Order management features coming soon...</p>
                    </div>
                </div>

                <div id="users-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">User Management</h3>
                        <p class="text-gray-600">User management features coming soon...</p>
                    </div>
                </div>

                <div id="analytics-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Analytics</h3>
                        <p class="text-gray-600">Advanced analytics coming soon...</p>
                    </div>
                </div>

                <div id="settings-section" class="section hidden">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">Settings</h3>
                        <p class="text-gray-600">Settings panel coming soon...</p>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="admin.js"></script>
</body>
</html>
