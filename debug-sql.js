const { executeQuery, connectDB, closeConnection } = require('./config/database');
const { calculatePagination } = require('./utils/helpers');

async function debugSQL() {
  try {
    await connectDB();
    console.log('🔍 Debugging SQL Parameters');
    console.log('============================');

    // Test pagination calculation
    const pagination = calculatePagination(1, 12, 100);
    console.log('Pagination object:', pagination);
    console.log('itemsPerPage type:', typeof pagination.itemsPerPage);
    console.log('offset type:', typeof pagination.offset);

    // Test simple query first
    console.log('\n1. Testing simple query...');
    const simpleQuery = 'SELECT COUNT(*) as total FROM products WHERE is_active = 1';
    const simpleResult = await executeQuery(simpleQuery, []);
    console.log('Simple query result:', simpleResult);

    // Test query with parameters
    console.log('\n2. Testing query with LIMIT/OFFSET...');
    const limitQuery = 'SELECT id, name FROM products WHERE is_active = 1 LIMIT ? OFFSET ?';
    const limitParams = [parseInt(pagination.itemsPerPage), parseInt(pagination.offset)];
    console.log('Parameters:', limitParams);
    console.log('Parameter types:', limitParams.map(p => typeof p));
    
    const limitResult = await executeQuery(limitQuery, limitParams);
    console.log('Limit query result count:', limitResult.length);

    // Test the actual problematic query
    console.log('\n3. Testing full products query...');
    const fullQuery = `
      SELECT 
        p.id,
        p.name,
        p.slug,
        p.short_description,
        p.price,
        p.sale_price,
        p.badge,
        p.is_featured,
        p.stock_quantity,
        p.colors,
        p.sizes,
        c.name as category_name,
        c.slug as category_slug,
        c.gender,
        (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
        (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
        (SELECT COUNT(*) FROM reviews WHERE product_id = p.id AND is_approved = 1) as review_count
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE p.is_active = 1
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const fullParams = [parseInt(pagination.itemsPerPage), parseInt(pagination.offset)];
    console.log('Full query parameters:', fullParams);
    
    const fullResult = await executeQuery(fullQuery, fullParams);
    console.log('Full query result count:', fullResult.length);

    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Error details:', error);
  } finally {
    await closeConnection();
  }
}

debugSQL();
