const express = require('express');
const path = require('path');

const app = express();
const PORT = 3003;

// Serve static files from the current directory
app.use(express.static('.'));

// Handle SPA routing - serve index.html for all routes that don't match static files
app.get('*', (req, res) => {
    // Check if the request is for a static file
    const ext = path.extname(req.path);
    if (ext) {
        // If it has an extension, it's likely a static file request
        res.status(404).send('File not found');
    } else {
        // Otherwise, serve index.html for SPA routing
        res.sendFile(path.join(__dirname, 'index.html'));
    }
});

app.listen(PORT, () => {
    console.log(`🎨 Luxe Fashion Frontend Server running on http://localhost:${PORT}`);
    console.log(`📱 Open your browser and navigate to: http://localhost:${PORT}`);
    console.log(`🔗 Backend API is running on: http://localhost:3002`);
    console.log('');
    console.log('📋 Available Pages:');
    console.log(`   🏠 Home: http://localhost:${PORT}`);
    console.log(`   👗 Products: http://localhost:${PORT}/products.html`);
    console.log(`   🛒 Checkout: http://localhost:${PORT}/checkout.html`);
    console.log('');
    console.log('🔧 To stop the server, press Ctrl+C');
});
