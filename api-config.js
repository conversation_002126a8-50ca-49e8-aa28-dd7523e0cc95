// API Configuration
const API_CONFIG = {
    BASE_URL: 'http://localhost:3002',
    ENDPOINTS: {
        HEALTH: '/health',
        PRODUCTS: '/api/products',
        PRODUCT_BY_ID: '/api/products',
        PRODUCT_BY_SLUG: '/api/products',
        PRODUCTS_BY_CATEGORY: '/api/products/category',
        SEARCH_PRODUCTS: '/api/products/search',
        AUTH: {
            LOGIN: '/api/auth/login',
            REGISTER: '/api/auth/register',
            ME: '/api/auth/me'
        },
        CART: {
            GET: '/api/cart',
            ADD: '/api/cart/add',
            UPDATE: '/api/cart/update',
            REMOVE: '/api/cart/remove'
        },
        WISHLIST: {
            GET: '/api/wishlist',
            ADD: '/api/wishlist/add',
            REMOVE: '/api/wishlist/remove'
        },
        NEWSLETTER: '/api/newsletter/subscribe'
    }
};

// API Helper Functions
class APIClient {
    constructor() {
        this.baseURL = API_CONFIG.BASE_URL;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Product API methods
    async getProducts(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `${API_CONFIG.ENDPOINTS.PRODUCTS}?${queryString}` : API_CONFIG.ENDPOINTS.PRODUCTS;
        return this.request(endpoint);
    }

    async getProductById(id) {
        return this.request(`${API_CONFIG.ENDPOINTS.PRODUCT_BY_ID}/${id}`);
    }

    async getProductBySlug(slug) {
        return this.request(`${API_CONFIG.ENDPOINTS.PRODUCT_BY_SLUG}/${slug}`);
    }

    async getProductsByCategory(category, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? 
            `${API_CONFIG.ENDPOINTS.PRODUCTS_BY_CATEGORY}/${category}?${queryString}` : 
            `${API_CONFIG.ENDPOINTS.PRODUCTS_BY_CATEGORY}/${category}`;
        return this.request(endpoint);
    }

    async searchProducts(query, params = {}) {
        const searchParams = new URLSearchParams({ q: query, ...params }).toString();
        return this.request(`${API_CONFIG.ENDPOINTS.SEARCH_PRODUCTS}?${searchParams}`);
    }

    // Health check
    async healthCheck() {
        return this.request(API_CONFIG.ENDPOINTS.HEALTH);
    }

    // Auth methods (for future use)
    async login(credentials) {
        return this.request(API_CONFIG.ENDPOINTS.AUTH.LOGIN, {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
    }

    async register(userData) {
        return this.request(API_CONFIG.ENDPOINTS.AUTH.REGISTER, {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    // Newsletter subscription
    async subscribeNewsletter(email) {
        return this.request(API_CONFIG.ENDPOINTS.NEWSLETTER, {
            method: 'POST',
            body: JSON.stringify({ email })
        });
    }
}

// Create global API client instance
window.apiClient = new APIClient();

// Utility function to show loading state
function showLoading(element) {
    if (element) {
        element.innerHTML = `
            <div class="flex items-center justify-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                <span class="ml-3 text-gray-600">Loading...</span>
            </div>
        `;
    }
}

// Utility function to show error state
function showError(element, message) {
    if (element) {
        element.innerHTML = `
            <div class="text-center py-12">
                <div class="text-red-500 text-xl mb-4">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Oops! Something went wrong</h3>
                <p class="text-gray-600 mb-4">${message}</p>
                <button onclick="location.reload()" class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-accent transition-colors duration-300">
                    Try Again
                </button>
            </div>
        `;
    }
}
