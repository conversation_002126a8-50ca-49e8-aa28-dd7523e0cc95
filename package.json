{"name": "luxe-fashion-backend", "version": "1.0.0", "description": "Backend API for Luxe Fashion e-commerce website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "frontend": "node frontend-server.js", "test": "jest", "db:migrate": "node database/migrate.js", "db:seed": "node database/seed.js", "db:setup": "node setup-database.js", "db:test": "node test-db-connection.js"}, "keywords": ["ecommerce", "fashion", "api", "nodejs", "express", "mysql"], "author": "Luxe Fashion Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}