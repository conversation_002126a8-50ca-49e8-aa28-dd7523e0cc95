const { executeQuery, connectDB, closeConnection } = require('./config/database');

async function checkData() {
    try {
        await connectDB();
        console.log('🔍 Checking database data...\n');

        // Check products
        const products = await executeQuery('SELECT id, name, colors, sizes FROM products LIMIT 3');
        console.log('📊 Products in database:');
        products.forEach(product => {
            console.log(`  ID: ${product.id}`);
            console.log(`  Name: ${product.name}`);
            console.log(`  Colors (raw): ${product.colors}`);
            console.log(`  Sizes (raw): ${product.sizes}`);
            console.log(`  Colors type: ${typeof product.colors}`);
            console.log(`  Sizes type: ${typeof product.sizes}`);
            console.log('  ---');
        });

        // Check categories
        const categories = await executeQuery('SELECT id, name, slug FROM categories LIMIT 5');
        console.log('\n📊 Categories in database:');
        categories.forEach(cat => {
            console.log(`  ${cat.id}: ${cat.name} (${cat.slug})`);
        });

        await closeConnection();
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

checkData();
