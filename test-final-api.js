const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

async function testAPI() {
  console.log('🎉 Final API Test - Luxe Fashion Backend');
  console.log('========================================');

  try {
    // Test health endpoint
    const health = await makeRequest('/health');
    console.log(`✅ Health Check: ${health.status} - ${health.data.message}`);

    // Test products list
    const products = await makeRequest('/api/products');
    console.log(`✅ Products List: ${products.status} - Found ${products.data.data.length} products`);

    // Test individual product
    const product = await makeRequest('/api/products/1');
    console.log(`✅ Single Product: ${product.status} - ${product.data.data.name}`);

    // Test category products
    const category = await makeRequest('/api/products/category/dresses');
    console.log(`✅ Category Products: ${category.status} - Found ${category.data.data.products.length} dresses`);

    // Test search
    const search = await makeRequest('/api/products/search?q=dress');
    console.log(`✅ Search Products: ${search.status} - ${search.data.message}`);

    // Test 404
    const notFound = await makeRequest('/api/nonexistent');
    console.log(`✅ 404 Handler: ${notFound.status} - ${notFound.data.message}`);

    console.log('\n🎯 All tests passed! Your API is working perfectly!');
    console.log('\n📋 Available Endpoints:');
    console.log('   - GET /health - Health check');
    console.log('   - GET /api/products - List all products');
    console.log('   - GET /api/products/:id - Get product by ID');
    console.log('   - GET /api/products/:slug - Get product by slug');
    console.log('   - GET /api/products/category/:slug - Get products by category');
    console.log('   - GET /api/products/search?q=term - Search products');
    console.log('   - GET /api/auth/* - Authentication endpoints');
    console.log('   - GET /api/cart/* - Cart management');
    console.log('   - GET /api/orders/* - Order management');
    console.log('   - GET /api/users/* - User management');
    console.log('   - GET /api/wishlist/* - Wishlist management');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAPI();
