/**
 * Internal assertion helpers.
 * @module
 * @deprecated
 */
import {
  abytes as ab,
  aexists as ae,
  anumber as an,
  aoutput as ao,
  type IHash as H,
} from './utils.ts';
/** @deprecated Use import from `noble/hashes/utils` module */
export const abytes: typeof ab = ab;
/** @deprecated Use import from `noble/hashes/utils` module */
export const aexists: typeof ae = ae;
/** @deprecated Use import from `noble/hashes/utils` module */
export const anumber: typeof an = an;
/** @deprecated Use import from `noble/hashes/utils` module */
export const aoutput: typeof ao = ao;
/** @deprecated Use import from `noble/hashes/utils` module */
export type Hash = H;
