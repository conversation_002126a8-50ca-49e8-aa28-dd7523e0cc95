const { executeQuery, connectDB, closeConnection } = require('./config/database');

async function fixAdminRole() {
    try {
        await connectDB();
        console.log('🔧 Fixing admin user role...');

        // Update admin user role
        await executeQuery(
            'UPDATE users SET role = ? WHERE email = ?',
            ['admin', '<EMAIL>']
        );

        console.log('✅ Admin user role updated to "admin"');

        // Verify the change
        const adminUser = await executeQuery(
            'SELECT id, email, first_name, last_name, role FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        if (adminUser.length > 0) {
            const user = adminUser[0];
            console.log('✅ Verification:');
            console.log(`   Email: ${user.email}`);
            console.log(`   Role: ${user.role}`);
        }

        console.log('\n🎯 Admin role fix completed!');

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await closeConnection();
    }
}

fixAdminRole();
