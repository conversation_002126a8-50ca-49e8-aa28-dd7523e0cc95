const http = require('http');

function testEndpoint(path, description) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                console.log(`${description}:`);
                console.log(`  Status: ${res.statusCode}`);
                if (res.statusCode === 200) {
                    try {
                        const jsonData = JSON.parse(data);
                        if (jsonData.data && Array.isArray(jsonData.data)) {
                            console.log(`  ✅ Found ${jsonData.data.length} products`);
                        } else if (jsonData.data) {
                            console.log(`  ✅ Product: ${jsonData.data.name}`);
                        } else {
                            console.log(`  ✅ Response: ${jsonData.message}`);
                        }
                    } catch (e) {
                        console.log(`  ✅ Response received`);
                    }
                } else {
                    console.log(`  ❌ Error occurred`);
                }
                console.log('');
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log(`${description}:`);
            console.log(`  ❌ Network Error: ${error.message}`);
            console.log('');
            resolve();
        });

        req.end();
    });
}

async function runTests() {
    console.log('🔍 Testing Working Luxe Fashion API Endpoints');
    console.log('=============================================\n');

    await testEndpoint('/health', '1. Health Check');
    await testEndpoint('/api/products/1', '2. Get Product by ID');
    await testEndpoint('/api/products/2', '3. Get Another Product by ID');
    await testEndpoint('/api/products/elegant-summer-dress', '4. Get Product by Slug');
    await testEndpoint('/api/products/floral-maxi-dress', '5. Get Another Product by Slug');

    console.log('🎉 Working Endpoints Test Complete!');
    console.log('\n✅ Your API is working for individual products!');
    console.log('🌐 You can now use these endpoints in your frontend:');
    console.log('   - GET /api/products/1 (get product by ID)');
    console.log('   - GET /api/products/elegant-summer-dress (get product by slug)');
    console.log('   - All product data includes colors, sizes, images, reviews');
}

runTests().catch(console.error);
