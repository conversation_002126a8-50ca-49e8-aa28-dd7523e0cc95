-- Migration script to add user roles to existing database
-- Run this script to update existing users table with role column

USE luxe_fashion;

-- Add role column to users table if it doesn't exist
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS role ENUM('user', 'admin', 'super_admin') DEFAULT 'user' AFTER phone;

-- Add index for role column
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_role (role);

-- Create a default admin user (password: admin123)
-- Password hash for 'admin123' with bcrypt rounds 12
INSERT IGNORE INTO users (
    email, 
    password, 
    first_name, 
    last_name, 
    role, 
    is_verified
) VALUES (
    '<EMAIL>',
    '$2a$12$LQv3c1yqBw2fyuQoiaj62OHZgUzaHpDNuhn0u3WgAkUkrPHuIu5jS',
    'Admin',
    'User',
    'admin',
    TRUE
);

-- Create a super admin user (password: superadmin123)
-- Password hash for 'superadmin123' with bcrypt rounds 12
INSERT IGNORE INTO users (
    email, 
    password, 
    first_name, 
    last_name, 
    role, 
    is_verified
) VALUES (
    '<EMAIL>',
    '$2a$12$8K1p/a0dHTBS89wEAWlbiOinfy2v2FlQRR8r8yvJ1N4KzO7jlvEYe',
    'Super',
    'Admin',
    'super_admin',
    TRUE
);

-- Update any existing users with email containing 'admin' to have admin role
UPDATE users 
SET role = 'admin' 
WHERE email LIKE '%admin%' AND role = 'user';

SELECT 'User roles migration completed successfully!' as message;
