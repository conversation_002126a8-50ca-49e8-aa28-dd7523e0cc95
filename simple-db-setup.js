const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function simpleSetup() {
    console.log('🚀 Simple Database Setup for Luxe Fashion');
    console.log('=========================================\n');

    try {
        // Test connection with current credentials
        console.log('🔄 Testing database connection...');
        
        const config = {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT) || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            multipleStatements: true
        };

        console.log(`📋 Connecting to: ${config.user}@${config.host}:${config.port}`);
        
        let connection;
        try {
            connection = await mysql.createConnection(config);
            console.log('✅ Connected to MySQL server successfully!');
        } catch (error) {
            if (error.code === 'ER_ACCESS_DENIED_ERROR') {
                console.error('❌ Access denied. Wrong password!');
                console.log('\n💡 Solutions:');
                console.log('   1. Update DB_PASSWORD in .env file with correct password');
                console.log('   2. Reset MySQL root password as Administrator');
                console.log('   3. Run: node test-your-password.js to find correct password');
                process.exit(1);
            }
            throw error;
        }

        // Check if database exists
        console.log('\n🔄 Checking if database exists...');
        const dbName = process.env.DB_NAME || 'luxe_fashion';
        
        const [databases] = await connection.execute(`SHOW DATABASES`);
        const dbExists = databases.some(db => Object.values(db)[0] === dbName);
        
        if (!dbExists) {
            console.log(`📊 Database '${dbName}' does not exist. Creating...`);
            await connection.execute(`CREATE DATABASE ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
            console.log('✅ Database created successfully!');
        } else {
            console.log(`✅ Database '${dbName}' already exists!`);
        }

        // Close current connection and reconnect to the specific database
        await connection.end();

        // Reconnect with database specified
        connection = await mysql.createConnection({
            ...config,
            database: dbName
        });

        // Check if tables exist
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`📊 Found ${tables.length} tables in the database`);
        
        if (tables.length === 0) {
            console.log('🔄 No tables found. Creating tables...');
            
            // Read and execute schema file
            const schemaPath = path.join(__dirname, 'database', 'schema.sql');
            if (!fs.existsSync(schemaPath)) {
                throw new Error(`Schema file not found: ${schemaPath}`);
            }
            
            const schema = fs.readFileSync(schemaPath, 'utf8');
            
            // Execute the entire schema at once using query (supports multiple statements)
            try {
                await connection.query(schema);
            } catch (error) {
                console.log(`⚠️  Warning executing schema: ${error.message}`);
                // Try executing statements individually as fallback
                const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);
                for (let i = 0; i < statements.length; i++) {
                    const statement = statements[i].trim();
                    if (statement) {
                        try {
                            await connection.query(statement);
                        } catch (stmtError) {
                            console.log(`⚠️  Warning executing statement ${i + 1}: ${stmtError.message}`);
                        }
                    }
                }
            }
            
            console.log('✅ Tables created successfully!');
        } else {
            console.log('✅ Tables already exist!');
            tables.forEach(table => {
                console.log(`   - ${Object.values(table)[0]}`);
            });
        }

        // Test final connection
        console.log('\n🔄 Testing final application connection...');
        await connection.end();
        
        const appConnection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT) || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: dbName
        });
        
        const [result] = await appConnection.execute('SELECT 1 as test');
        console.log('✅ Application database connection successful!');
        console.log(`📊 Test result: ${JSON.stringify(result[0])}`);
        
        await appConnection.end();

        console.log('\n🎉 Database setup completed successfully!');
        console.log('\n📋 Next steps:');
        console.log('   1. Run: npm start');
        console.log('   2. Your API will be available at: http://localhost:3000');
        console.log('   3. Health check: http://localhost:3000/health');
        console.log('   4. Frontend is already open in your browser');
        console.log('\n💡 Optional: Run "npm run db:seed" to add sample data');

    } catch (error) {
        console.error('\n❌ Database setup failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Check MySQL service: Get-Service MySQL93');
        console.log('   2. Verify password: node test-your-password.js');
        console.log('   3. Check .env file settings');
        process.exit(1);
    }
}

// Run setup if this file is executed directly
if (require.main === module) {
    simpleSetup();
}

module.exports = { simpleSetup };
