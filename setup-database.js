const mysql = require('mysql2/promise');
const readline = require('readline');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setupDatabase() {
  console.log('🚀 Luxe Fashion Database Setup');
  console.log('================================\n');

  try {
    // Get MySQL root password
    const rootPassword = await question('Enter MySQL root password (press Enter if no password): ');
    
    // Test connection to MySQL server
    console.log('\n🔄 Testing MySQL connection...');
    
    const connectionConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3306,
      user: process.env.DB_USER || 'root',
      password: rootPassword,
      multipleStatements: true
    };

    let connection;
    try {
      connection = await mysql.createConnection(connectionConfig);
      console.log('✅ Connected to MySQL server successfully!');
    } catch (error) {
      if (error.code === 'ER_ACCESS_DENIED_ERROR') {
        console.error('❌ Access denied. Please check your MySQL root password.');
        console.log('\n💡 If you forgot your MySQL root password, you can reset it:');
        console.log('   1. Stop MySQL service: net stop MySQL93');
        console.log('   2. Start MySQL in safe mode and reset password');
        console.log('   3. Or reinstall MySQL with a known password');
        process.exit(1);
      }
      throw error;
    }

    // Update .env file with the working password
    if (rootPassword) {
      console.log('\n🔄 Updating .env file with database password...');
      const envPath = path.join(__dirname, '.env');
      let envContent = fs.readFileSync(envPath, 'utf8');
      envContent = envContent.replace(/^DB_PASSWORD=.*$/m, `DB_PASSWORD=${rootPassword}`);
      fs.writeFileSync(envPath, envContent);
      console.log('✅ .env file updated successfully!');
    }

    // Check if database exists
    console.log('\n🔄 Checking if database exists...');
    const dbName = process.env.DB_NAME || 'luxe_fashion';
    const [databases] = await connection.execute(`SHOW DATABASES LIKE '${dbName}'`);
    
    if (databases.length === 0) {
      console.log('📊 Database does not exist. Creating database and tables...');
      
      // Read and execute schema file
      const schemaPath = path.join(__dirname, 'database', 'schema.sql');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      await connection.query(schema);
      console.log('✅ Database and tables created successfully!');
    } else {
      console.log('✅ Database already exists!');
      
      // Switch to the database and check tables
      await connection.execute(`USE ${process.env.DB_NAME || 'luxe_fashion'}`);
      const [tables] = await connection.execute('SHOW TABLES');
      console.log(`📊 Found ${tables.length} tables in the database`);
      
      if (tables.length === 0) {
        console.log('🔄 No tables found. Running schema creation...');
        const schemaPath = path.join(__dirname, 'database', 'schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');
        await connection.query(schema);
        console.log('✅ Tables created successfully!');
      }
    }

    // Test the application database connection
    console.log('\n🔄 Testing application database connection...');
    await connection.end();
    
    // Test with the application's database config
    const appConnection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3306,
      user: process.env.DB_USER || 'root',
      password: rootPassword,
      database: process.env.DB_NAME || 'luxe_fashion'
    });
    
    const [result] = await appConnection.execute('SELECT 1 as test');
    console.log('✅ Application database connection successful!');
    await appConnection.end();

    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Run: npm start');
    console.log('   2. Your API will be available at: http://localhost:3000');
    console.log('   3. Health check: http://localhost:3000/health');
    console.log('\n💡 Optional: Run "npm run db:seed" to add sample data');

  } catch (error) {
    console.error('\n❌ Database setup failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('   1. Make sure MySQL service is running: Get-Service MySQL93');
    console.log('   2. Check your MySQL root password');
    console.log('   3. Verify MySQL is listening on port 3306');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run setup if this file is executed directly
if (require.main === module) {
  setupDatabase();
}

module.exports = { setupDatabase };
