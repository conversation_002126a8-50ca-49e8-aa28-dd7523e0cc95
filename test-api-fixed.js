const http = require('http');

function testEndpoint(path, description) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log(`\n${description}:`);
        console.log(`  Status: ${res.statusCode}`);
        try {
          const jsonData = JSON.parse(data);
          if (jsonData.success) {
            console.log(`  ✅ Success: ${jsonData.message}`);
          } else {
            console.log(`  ❌ Error: ${jsonData.message}`);
          }
        } catch (e) {
          console.log(`  Response: ${data.substring(0, 100)}...`);
        }
        resolve();
      });
    });

    req.on('error', (err) => {
      console.log(`\n${description}:`);
      console.log(`  ❌ Connection Error: ${err.message}`);
      resolve();
    });

    req.end();
  });
}

async function runTests() {
  console.log('🔍 Testing Fixed Luxe Fashion API');
  console.log('=================================');

  await testEndpoint('/health', '1. Health Check');
  await testEndpoint('/api/products', '2. Get All Products');
  await testEndpoint('/api/products/1', '3. Get Product by ID');
  await testEndpoint('/api/products/elegant-summer-dress', '4. Get Product by Slug');
  await testEndpoint('/api/products/category/dresses', '5. Get Products by Category');
  await testEndpoint('/api/products/search?q=dress', '6. Search Products');
  await testEndpoint('/api/nonexistent', '7. Test 404 Route');

  console.log('\n🎯 Testing Complete!');
}

runTests().catch(console.error);
