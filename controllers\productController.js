const { executeQuery } = require('../config/database');
const { formatResponse, calculatePagination, createSlug } = require('../utils/helpers');

// Helper function to parse colors/sizes from database
const parseArrayField = (field) => {
  if (!field) return [];
  if (typeof field === 'string') {
    try {
      // Try to parse as JSON first
      return JSON.parse(field);
    } catch (e) {
      // If JSON parsing fails, treat as comma-separated string
      return field.split(',').map(item => item.trim()).filter(item => item.length > 0);
    }
  }
  return Array.isArray(field) ? field : [];
};

// @desc    Get all products with filtering and pagination
// @route   GET /api/products
// @access  Public
const getProducts = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 12,
      category,
      gender,
      min_price,
      max_price,
      sort = 'created_at',
      order = 'DESC',
      search,
      featured
    } = req.query;

    // Build WHERE clause
    let whereConditions = ['p.is_active = 1'];
    let queryParams = [];

    if (category) {
      whereConditions.push('c.slug = ?');
      queryParams.push(category);
    }

    if (gender) {
      whereConditions.push('c.gender IN (?, "unisex")');
      queryParams.push(gender);
    }

    if (min_price) {
      whereConditions.push('(p.sale_price IS NOT NULL AND p.sale_price >= ?) OR (p.sale_price IS NULL AND p.price >= ?)');
      queryParams.push(min_price, min_price);
    }

    if (max_price) {
      whereConditions.push('(p.sale_price IS NOT NULL AND p.sale_price <= ?) OR (p.sale_price IS NULL AND p.price <= ?)');
      queryParams.push(max_price, max_price);
    }

    if (featured === 'true') {
      whereConditions.push('p.is_featured = 1');
    }

    if (search) {
      whereConditions.push('MATCH(p.name, p.description, p.short_description, p.brand) AGAINST(? IN NATURAL LANGUAGE MODE)');
      queryParams.push(search);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Build ORDER BY clause
    const validSortFields = ['name', 'price', 'created_at', 'rating'];
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, queryParams);
    const totalCount = countResult[0].total;

    // Calculate pagination
    const pagination = calculatePagination(page, limit, totalCount);

    // Get products
    const productsQuery = `
      SELECT 
        p.id,
        p.name,
        p.slug,
        p.short_description,
        p.price,
        p.sale_price,
        p.badge,
        p.is_featured,
        p.stock_quantity,
        p.colors,
        p.sizes,
        c.name as category_name,
        c.slug as category_slug,
        c.gender,
        (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
        (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
        (SELECT COUNT(*) FROM reviews WHERE product_id = p.id AND is_approved = 1) as review_count
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      ${whereClause}
      ORDER BY ${sortField === 'rating' ? 'avg_rating' : `p.${sortField}`} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    const products = await executeQuery(productsQuery, [...queryParams, pagination.itemsPerPage, pagination.offset]);

    // Format products
    const formattedProducts = products.map(product => ({
      ...product,
      colors: parseArrayField(product.colors),
      sizes: parseArrayField(product.sizes),
      avg_rating: product.avg_rating ? parseFloat(product.avg_rating).toFixed(1) : null,
      review_count: parseInt(product.review_count) || 0,
      final_price: product.sale_price || product.price,
      discount_percentage: product.sale_price ?
        Math.round(((product.price - product.sale_price) / product.price) * 100) : 0
    }));

    res.status(200).json(
      formatResponse(true, 'Products retrieved successfully', formattedProducts, pagination)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get single product by ID or slug
// @route   GET /api/products/:identifier
// @access  Public
const getProduct = async (req, res, next) => {
  try {
    const { identifier } = req.params;
    
    // Check if identifier is numeric (ID) or string (slug)
    const isId = /^\d+$/.test(identifier);
    const field = isId ? 'p.id' : 'p.slug';

    const productQuery = `
      SELECT 
        p.*,
        c.name as category_name,
        c.slug as category_slug,
        c.gender,
        (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
        (SELECT COUNT(*) FROM reviews WHERE product_id = p.id AND is_approved = 1) as review_count
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE ${field} = ? AND p.is_active = 1
    `;

    const products = await executeQuery(productQuery, [identifier]);

    if (products.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Product not found')
      );
    }

    const product = products[0];

    // Get product images
    const images = await executeQuery(
      'SELECT image_url, alt_text, is_primary FROM product_images WHERE product_id = ? ORDER BY is_primary DESC, sort_order ASC',
      [product.id]
    );

    // Get recent reviews
    const reviews = await executeQuery(
      `SELECT r.*, u.first_name, u.last_name 
       FROM reviews r 
       LEFT JOIN users u ON r.user_id = u.id 
       WHERE r.product_id = ? AND r.is_approved = 1 
       ORDER BY r.created_at DESC 
       LIMIT 5`,
      [product.id]
    );

    // Format product
    const formattedProduct = {
      ...product,
      colors: parseArrayField(product.colors),
      sizes: parseArrayField(product.sizes),
      materials: parseArrayField(product.materials),
      dimensions: product.dimensions ? (typeof product.dimensions === 'string' ? JSON.parse(product.dimensions) : product.dimensions) : {},
      avg_rating: product.avg_rating ? parseFloat(product.avg_rating).toFixed(1) : null,
      review_count: parseInt(product.review_count) || 0,
      final_price: product.sale_price || product.price,
      discount_percentage: product.sale_price ?
        Math.round(((product.price - product.sale_price) / product.price) * 100) : 0,
      images,
      reviews: reviews.map(review => ({
        ...review,
        reviewer_name: `${review.first_name} ${review.last_name.charAt(0)}.`
      }))
    };

    res.status(200).json(
      formatResponse(true, 'Product retrieved successfully', formattedProduct)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get products by category
// @route   GET /api/products/category/:slug
// @access  Public
const getProductsByCategory = async (req, res, next) => {
  try {
    const { slug } = req.params;
    const { page = 1, limit = 12, sort = 'created_at', order = 'DESC' } = req.query;

    // Get category
    const categories = await executeQuery(
      'SELECT * FROM categories WHERE slug = ? AND is_active = 1',
      [slug]
    );

    if (categories.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Category not found')
      );
    }

    const category = categories[0];

    // Get total count
    const countResult = await executeQuery(
      'SELECT COUNT(*) as total FROM products WHERE category_id = ? AND is_active = 1',
      [category.id]
    );
    const totalCount = countResult[0].total;

    // Calculate pagination
    const pagination = calculatePagination(page, limit, totalCount);

    // Get products
    const validSortFields = ['name', 'price', 'created_at'];
    const sortField = validSortFields.includes(sort) ? sort : 'created_at';
    const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const products = await executeQuery(
      `SELECT 
        p.id, p.name, p.slug, p.short_description, p.price, p.sale_price, 
        p.badge, p.is_featured, p.stock_quantity, p.colors, p.sizes,
        (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
        (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
        (SELECT COUNT(*) FROM reviews WHERE product_id = p.id AND is_approved = 1) as review_count
       FROM products p
       WHERE p.category_id = ? AND p.is_active = 1
       ORDER BY p.${sortField} ${sortOrder}
       LIMIT ? OFFSET ?`,
      [category.id, pagination.itemsPerPage, pagination.offset]
    );

    // Format products
    const formattedProducts = products.map(product => ({
      ...product,
      colors: parseArrayField(product.colors),
      sizes: parseArrayField(product.sizes),
      avg_rating: product.avg_rating ? parseFloat(product.avg_rating).toFixed(1) : null,
      review_count: parseInt(product.review_count) || 0,
      final_price: product.sale_price || product.price,
      discount_percentage: product.sale_price ?
        Math.round(((product.price - product.sale_price) / product.price) * 100) : 0
    }));

    res.status(200).json(
      formatResponse(true, 'Products retrieved successfully', {
        category,
        products: formattedProducts
      }, pagination)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Search products
// @route   GET /api/products/search
// @access  Public
const searchProducts = async (req, res, next) => {
  try {
    const { q, page = 1, limit = 12 } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json(
        formatResponse(false, 'Search query must be at least 2 characters long')
      );
    }

    const searchTerm = q.trim();

    // Get total count
    const countResult = await executeQuery(
      `SELECT COUNT(*) as total 
       FROM products p 
       WHERE p.is_active = 1 AND (
         MATCH(p.name, p.description, p.short_description, p.brand) AGAINST(? IN NATURAL LANGUAGE MODE)
         OR p.name LIKE ?
         OR p.brand LIKE ?
       )`,
      [searchTerm, `%${searchTerm}%`, `%${searchTerm}%`]
    );
    const totalCount = countResult[0].total;

    // Calculate pagination
    const pagination = calculatePagination(page, limit, totalCount);

    // Search products
    const products = await executeQuery(
      `SELECT 
        p.id, p.name, p.slug, p.short_description, p.price, p.sale_price, 
        p.badge, p.brand, p.colors, p.sizes,
        c.name as category_name, c.slug as category_slug,
        (SELECT image_url FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image,
        (SELECT AVG(rating) FROM reviews WHERE product_id = p.id AND is_approved = 1) as avg_rating,
        (SELECT COUNT(*) FROM reviews WHERE product_id = p.id AND is_approved = 1) as review_count,
        MATCH(p.name, p.description, p.short_description, p.brand) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.is_active = 1 AND (
         MATCH(p.name, p.description, p.short_description, p.brand) AGAINST(? IN NATURAL LANGUAGE MODE)
         OR p.name LIKE ?
         OR p.brand LIKE ?
       )
       ORDER BY relevance DESC, p.is_featured DESC, p.created_at DESC
       LIMIT ? OFFSET ?`,
      [searchTerm, searchTerm, `%${searchTerm}%`, `%${searchTerm}%`, pagination.itemsPerPage, pagination.offset]
    );

    // Format products
    const formattedProducts = products.map(product => ({
      ...product,
      colors: parseArrayField(product.colors),
      sizes: parseArrayField(product.sizes),
      avg_rating: product.avg_rating ? parseFloat(product.avg_rating).toFixed(1) : null,
      review_count: parseInt(product.review_count) || 0,
      final_price: product.sale_price || product.price,
      discount_percentage: product.sale_price ?
        Math.round(((product.price - product.sale_price) / product.price) * 100) : 0
    }));

    res.status(200).json(
      formatResponse(true, `Found ${totalCount} products for "${searchTerm}"`, formattedProducts, pagination)
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getProducts,
  getProduct,
  getProductsByCategory,
  searchProducts
};
