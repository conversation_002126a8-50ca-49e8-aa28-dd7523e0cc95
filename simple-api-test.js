const http = require('http');

function testEndpoint(path, description) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: path,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                console.log(`${description}:`);
                console.log(`  Status: ${res.statusCode}`);
                try {
                    const jsonData = JSON.parse(data);
                    console.log(`  Response: ${JSON.stringify(jsonData, null, 2)}`);
                } catch (e) {
                    console.log(`  Response: ${data}`);
                }
                console.log('');
                resolve();
            });
        });

        req.on('error', (error) => {
            console.log(`${description}:`);
            console.log(`  Error: ${error.message}`);
            console.log('');
            resolve();
        });

        req.end();
    });
}

async function runTests() {
    console.log('🔍 Testing Luxe Fashion API Endpoints');
    console.log('=====================================\n');

    await testEndpoint('/health', '1. Health Check');
    await testEndpoint('/api/products', '2. Get All Products');
    await testEndpoint('/api/products/1', '3. Get Product by ID');
    await testEndpoint('/api/products/elegant-summer-dress', '4. Get Product by Slug');
    await testEndpoint('/api/products/category/dresses', '5. Get Products by Category');
    await testEndpoint('/api/products/search?q=dress', '6. Search Products');
    await testEndpoint('/api', '7. API Base Route');
    await testEndpoint('/', '8. Root Route');

    console.log('🎯 Testing Complete!');
    console.log('\n💡 If you see "Route not found" errors, it means:');
    console.log('   1. The route doesn\'t exist');
    console.log('   2. There\'s an issue with the controller');
    console.log('   3. The middleware is blocking the request');
}

runTests().catch(console.error);
