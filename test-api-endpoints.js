const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testEndpoints() {
    console.log('🔍 Testing Luxe Fashion API Endpoints');
    console.log('=====================================\n');

    const endpoints = [
        { method: 'GET', url: '/health', description: 'Health Check' },
        { method: 'GET', url: '/api/products', description: 'Get All Products' },
        { method: 'GET', url: '/api/products/1', description: 'Get Product by ID' },
        { method: 'GET', url: '/api/products/elegant-summer-dress', description: 'Get Product by Slug' },
        { method: 'GET', url: '/api/products/category/dresses', description: 'Get Products by Category' },
        { method: 'GET', url: '/api/products/search?q=dress', description: 'Search Products' },
        { method: 'GET', url: '/api/auth/me', description: 'Get Current User (should fail without auth)' },
        { method: 'POST', url: '/api/newsletter/subscribe', description: 'Newsletter Subscribe', data: { email: '<EMAIL>' } }
    ];

    for (const endpoint of endpoints) {
        try {
            console.log(`🔄 Testing: ${endpoint.method} ${endpoint.url}`);
            console.log(`   Description: ${endpoint.description}`);
            
            let response;
            if (endpoint.method === 'GET') {
                response = await axios.get(`${BASE_URL}${endpoint.url}`);
            } else if (endpoint.method === 'POST') {
                response = await axios.post(`${BASE_URL}${endpoint.url}`, endpoint.data || {});
            }
            
            console.log(`   ✅ Status: ${response.status}`);
            
            if (endpoint.url === '/health') {
                console.log(`   📊 Response: ${JSON.stringify(response.data)}`);
            } else if (endpoint.url === '/api/products') {
                console.log(`   📊 Products found: ${response.data.data ? response.data.data.length : 'N/A'}`);
            } else if (response.data.data) {
                console.log(`   📊 Data type: ${Array.isArray(response.data.data) ? 'Array' : 'Object'}`);
            } else {
                console.log(`   📊 Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
            }
            
        } catch (error) {
            if (error.response) {
                console.log(`   ❌ Status: ${error.response.status}`);
                console.log(`   📊 Error: ${JSON.stringify(error.response.data)}`);
            } else {
                console.log(`   ❌ Network Error: ${error.message}`);
            }
        }
        
        console.log('');
    }

    console.log('🎯 API Testing Complete!');
    console.log('\n💡 Available API Endpoints:');
    console.log('   GET  /health - Health check');
    console.log('   GET  /api/products - List all products');
    console.log('   GET  /api/products/:id - Get product by ID');
    console.log('   GET  /api/products/category/:slug - Get products by category');
    console.log('   GET  /api/products/search?q=term - Search products');
    console.log('   POST /api/auth/register - Register user');
    console.log('   POST /api/auth/login - Login user');
    console.log('   POST /api/newsletter/subscribe - Subscribe to newsletter');
    console.log('   GET  /api/cart - Get cart (requires auth)');
    console.log('   POST /api/cart/add - Add to cart (requires auth)');
}

testEndpoints().catch(console.error);
