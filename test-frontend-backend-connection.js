const http = require('http');

function testEndpoint(host, port, path, description) {
  return new Promise((resolve) => {
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log(`\n${description}:`);
        console.log(`  Status: ${res.statusCode}`);
        if (res.statusCode === 200) {
          console.log(`  ✅ Success`);
          if (path.includes('api')) {
            try {
              const jsonData = JSON.parse(data);
              if (jsonData.success) {
                console.log(`  📊 Data: ${jsonData.message || 'API response received'}`);
              }
            } catch (e) {
              console.log(`  📊 Response received (${data.length} bytes)`);
            }
          } else {
            console.log(`  📊 HTML page loaded (${data.length} bytes)`);
          }
        } else {
          console.log(`  ❌ Error: ${res.statusCode}`);
        }
        resolve();
      });
    });

    req.on('error', (err) => {
      console.log(`\n${description}:`);
      console.log(`  ❌ Connection Error: ${err.message}`);
      resolve();
    });

    req.end();
  });
}

async function runTests() {
  console.log('🔍 Testing Frontend-Backend Connection');
  console.log('=====================================');

  // Test Backend API
  console.log('\n🔧 Backend API Tests:');
  await testEndpoint('localhost', 3002, '/health', '1. Backend Health Check');
  await testEndpoint('localhost', 3002, '/api/products', '2. Backend Products API');

  // Test Frontend Server
  console.log('\n🎨 Frontend Server Tests:');
  await testEndpoint('localhost', 3003, '/', '3. Frontend Home Page');
  await testEndpoint('localhost', 3003, '/products.html', '4. Frontend Products Page');
  await testEndpoint('localhost', 3003, '/api-config.js', '5. Frontend API Config');

  console.log('\n🎯 Connection Test Complete!');
  console.log('\n📋 Summary:');
  console.log('   🔧 Backend API: http://localhost:3002');
  console.log('   🎨 Frontend: http://localhost:3003');
  console.log('   🌐 Website: http://localhost:3003');
  console.log('   👗 Products: http://localhost:3003/products.html');
  console.log('\n✨ Your Luxe Fashion website should now be fully functional!');
}

runTests().catch(console.error);
