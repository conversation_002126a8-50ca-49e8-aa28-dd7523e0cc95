const http = require('http');

// Test admin login and get token
async function testAdminLogin() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            email: '<EMAIL>',
            password: 'admin123'
        });

        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    if (res.statusCode === 200 && jsonData.success) {
                        resolve(jsonData.data.token);
                    } else {
                        reject(new Error(jsonData.message || 'Login failed'));
                    }
                } catch (e) {
                    reject(new Error('Invalid response format'));
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.write(postData);
        req.end();
    });
}

// Test admin endpoint with token
function testAdminEndpoint(path, token, description) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: path,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                console.log(`\n${description}:`);
                console.log(`  Status: ${res.statusCode}`);
                try {
                    const jsonData = JSON.parse(data);
                    if (jsonData.success) {
                        console.log(`  ✅ Success: ${jsonData.message}`);
                        if (jsonData.data) {
                            if (jsonData.data.totals) {
                                console.log(`  📊 Users: ${jsonData.data.totals.users}, Products: ${jsonData.data.totals.products}, Orders: ${jsonData.data.totals.orders}`);
                            }
                        }
                    } else {
                        console.log(`  ❌ Error: ${jsonData.message}`);
                    }
                } catch (e) {
                    console.log(`  📄 Response: ${data.substring(0, 100)}...`);
                }
                resolve();
            });
        });

        req.on('error', (err) => {
            console.log(`\n${description}:`);
            console.log(`  ❌ Connection Error: ${err.message}`);
            resolve();
        });

        req.end();
    });
}

async function runAdminTests() {
    console.log('🔐 Testing Admin Dashboard API');
    console.log('==============================');

    try {
        // Test admin login
        console.log('\n🔑 Testing Admin Login...');
        const token = await testAdminLogin();
        console.log('✅ Admin login successful, token received');

        // Test admin endpoints
        await testAdminEndpoint('/api/admin/dashboard', token, '1. Dashboard Analytics');
        await testAdminEndpoint('/api/admin/users', token, '2. Users Management');
        await testAdminEndpoint('/api/admin/orders', token, '3. Orders Management');
        await testAdminEndpoint('/api/admin/products', token, '4. Products Management');

        console.log('\n🎯 Admin API Testing Complete!');
        console.log('\n📋 Admin Dashboard Ready:');
        console.log('   🌐 Frontend: http://localhost:3003/admin.html');
        console.log('   🔧 Backend: http://localhost:3001');
        console.log('   👤 Admin Email: <EMAIL>');
        console.log('   🔑 Password: admin123');

    } catch (error) {
        console.error('\n❌ Admin login failed:', error.message);
        console.log('\n🔧 Make sure the user roles migration was run successfully');
    }
}

runAdminTests().catch(console.error);
