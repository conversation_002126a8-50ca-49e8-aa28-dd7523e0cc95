const { executeQuery } = require('../config/database');
const { formatResponse, calculatePagination } = require('../utils/helpers');

// @desc    Get dashboard analytics
// @route   GET /api/admin/dashboard
// @access  Private/Admin
const getDashboardAnalytics = async (req, res, next) => {
  try {
    // Get total counts
    const [totalUsers] = await executeQuery('SELECT COUNT(*) as count FROM users');
    const [totalProducts] = await executeQuery('SELECT COUNT(*) as count FROM products');
    const [totalOrders] = await executeQuery('SELECT COUNT(*) as count FROM orders');
    const [totalRevenue] = await executeQuery('SELECT COALESCE(SUM(total_amount), 0) as total FROM orders WHERE payment_status = "paid"');

    // Get recent orders
    const recentOrders = await executeQuery(`
      SELECT o.id, o.order_number, o.total_amount, o.status, o.created_at,
             CONCAT(u.first_name, ' ', u.last_name) as customer_name,
             u.email as customer_email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      ORDER BY o.created_at DESC
      LIMIT 10
    `);

    // Get popular products
    const popularProducts = await executeQuery(`
      SELECT p.id, p.name, p.price, p.stock_quantity,
             COUNT(oi.id) as order_count,
             SUM(oi.quantity) as total_sold
      FROM products p
      LEFT JOIN order_items oi ON p.id = oi.product_id
      GROUP BY p.id
      ORDER BY total_sold DESC
      LIMIT 10
    `);

    // Get monthly sales data for chart
    const monthlySales = await executeQuery(`
      SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as order_count,
        SUM(total_amount) as revenue
      FROM orders 
      WHERE payment_status = 'paid' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month ASC
    `);

    // Get user registrations by month
    const userRegistrations = await executeQuery(`
      SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        COUNT(*) as user_count
      FROM users 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month ASC
    `);

    res.status(200).json(
      formatResponse(true, 'Dashboard analytics retrieved', {
        totals: {
          users: totalUsers.count,
          products: totalProducts.count,
          orders: totalOrders.count,
          revenue: totalRevenue.total
        },
        recentOrders,
        popularProducts,
        charts: {
          monthlySales,
          userRegistrations
        }
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get all users with pagination
// @route   GET /api/admin/users
// @access  Private/Admin
const getUsers = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, search = '', role = '' } = req.query;
    const pagination = calculatePagination(page, limit);

    let whereClause = 'WHERE 1=1';
    const queryParams = [];

    if (search) {
      whereClause += ' AND (first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (role) {
      whereClause += ' AND role = ?';
      queryParams.push(role);
    }

    // Get total count
    const totalQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const [{ total }] = await executeQuery(totalQuery, queryParams);

    // Get users
    const usersQuery = `
      SELECT id, email, first_name, last_name, phone, role, is_verified, created_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ${pagination.itemsPerPage} OFFSET ${pagination.offset}
    `;

    const users = await executeQuery(usersQuery, queryParams);

    res.status(200).json(
      formatResponse(true, 'Users retrieved successfully', {
        users,
        pagination: {
          ...pagination,
          total,
          totalPages: Math.ceil(total / pagination.itemsPerPage)
        }
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update user role
// @route   PUT /api/admin/users/:id/role
// @access  Private/Admin
const updateUserRole = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { role } = req.body;

    // Validate role
    const validRoles = ['user', 'admin', 'super_admin'];
    if (!validRoles.includes(role)) {
      return res.status(400).json(
        formatResponse(false, 'Invalid role specified')
      );
    }

    // Check if user exists
    const users = await executeQuery('SELECT id FROM users WHERE id = ?', [id]);
    if (users.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'User not found')
      );
    }

    // Update user role
    await executeQuery('UPDATE users SET role = ? WHERE id = ?', [role, id]);

    res.status(200).json(
      formatResponse(true, 'User role updated successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Delete user
// @route   DELETE /api/admin/users/:id
// @access  Private/Admin
const deleteUser = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if user exists
    const users = await executeQuery('SELECT id FROM users WHERE id = ?', [id]);
    if (users.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'User not found')
      );
    }

    // Delete user (cascade will handle related records)
    await executeQuery('DELETE FROM users WHERE id = ?', [id]);

    res.status(200).json(
      formatResponse(true, 'User deleted successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get all orders with pagination
// @route   GET /api/admin/orders
// @access  Private/Admin
const getOrders = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status = '', search = '' } = req.query;
    const pagination = calculatePagination(page, limit);

    let whereClause = 'WHERE 1=1';
    const queryParams = [];

    if (status) {
      whereClause += ' AND o.status = ?';
      queryParams.push(status);
    }

    if (search) {
      whereClause += ' AND (o.order_number LIKE ? OR u.email LIKE ? OR CONCAT(u.first_name, " ", u.last_name) LIKE ?)';
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    // Get total count
    const totalQuery = `
      SELECT COUNT(*) as total 
      FROM orders o 
      LEFT JOIN users u ON o.user_id = u.id 
      ${whereClause}
    `;
    const [{ total }] = await executeQuery(totalQuery, queryParams);

    // Get orders
    const ordersQuery = `
      SELECT o.*, 
             CONCAT(u.first_name, ' ', u.last_name) as customer_name,
             u.email as customer_email
      FROM orders o
      LEFT JOIN users u ON o.user_id = u.id
      ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT ${pagination.itemsPerPage} OFFSET ${pagination.offset}
    `;

    const orders = await executeQuery(ordersQuery, queryParams);

    res.status(200).json(
      formatResponse(true, 'Orders retrieved successfully', {
        orders,
        pagination: {
          ...pagination,
          total,
          totalPages: Math.ceil(total / pagination.itemsPerPage)
        }
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update order status
// @route   PUT /api/admin/orders/:id/status
// @access  Private/Admin
const updateOrderStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json(
        formatResponse(false, 'Invalid status specified')
      );
    }

    // Check if order exists
    const orders = await executeQuery('SELECT id FROM orders WHERE id = ?', [id]);
    if (orders.length === 0) {
      return res.status(404).json(
        formatResponse(false, 'Order not found')
      );
    }

    // Update order status
    const updateData = { status };
    if (status === 'shipped') {
      updateData.shipped_at = new Date();
    } else if (status === 'delivered') {
      updateData.delivered_at = new Date();
    }

    const updateFields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const updateValues = Object.values(updateData);

    await executeQuery(
      `UPDATE orders SET ${updateFields} WHERE id = ?`,
      [...updateValues, id]
    );

    res.status(200).json(
      formatResponse(true, 'Order status updated successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getDashboardAnalytics,
  getUsers,
  updateUserRole,
  deleteUser,
  getOrders,
  updateOrderStatus
};
