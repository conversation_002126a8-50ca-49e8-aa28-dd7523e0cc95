const { executeQuery, connectDB, closeConnection } = require('./config/database');

async function runUserRolesMigration() {
    try {
        await connectDB();
        console.log('🔄 Running user roles migration...');

        // Add role column to users table if it doesn't exist
        try {
            await executeQuery(`
                ALTER TABLE users 
                ADD COLUMN role ENUM('user', 'admin', 'super_admin') DEFAULT 'user' AFTER phone
            `);
            console.log('✅ Added role column to users table');
        } catch (error) {
            if (error.code === 'ER_DUP_FIELDNAME') {
                console.log('ℹ️  Role column already exists');
            } else {
                throw error;
            }
        }

        // Add index for role column
        try {
            await executeQuery(`ALTER TABLE users ADD INDEX idx_role (role)`);
            console.log('✅ Added index for role column');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('ℹ️  Role index already exists');
            } else {
                throw error;
            }
        }

        // Create default admin user
        try {
            await executeQuery(`
                INSERT INTO users (
                    email, 
                    password, 
                    first_name, 
                    last_name, 
                    role, 
                    is_verified
                ) VALUES (
                    '<EMAIL>',
                    '$2a$12$LQv3c1yqBw2fyuQoiaj62OHZgUzaHpDNuhn0u3WgAkUkrPHuIu5jS',
                    'Admin',
                    'User',
                    'admin',
                    TRUE
                )
            `);
            console.log('✅ Created admin user: <EMAIL> (password: admin123)');
        } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                console.log('ℹ️  Admin user already exists');
            } else {
                throw error;
            }
        }

        // Create super admin user
        try {
            await executeQuery(`
                INSERT INTO users (
                    email, 
                    password, 
                    first_name, 
                    last_name, 
                    role, 
                    is_verified
                ) VALUES (
                    '<EMAIL>',
                    '$2a$12$8K1p/a0dHTBS89wEAWlbiOinfy2v2FlQRR8r8yvJ1N4KzO7jlvEYe',
                    'Super',
                    'Admin',
                    'super_admin',
                    TRUE
                )
            `);
            console.log('✅ Created super admin user: <EMAIL> (password: superadmin123)');
        } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                console.log('ℹ️  Super admin user already exists');
            } else {
                throw error;
            }
        }

        console.log('\n🎉 User roles migration completed successfully!');
        console.log('\n👤 Admin Credentials:');
        console.log('   Email: <EMAIL>');
        console.log('   Password: admin123');
        console.log('\n👑 Super Admin Credentials:');
        console.log('   Email: <EMAIL>');
        console.log('   Password: superadmin123');

    } catch (error) {
        console.error('❌ Migration failed:', error);
    } finally {
        await closeConnection();
    }
}

runUserRolesMigration();
