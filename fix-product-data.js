const { executeQuery, connectDB, closeConnection } = require('./config/database');

async function fixProductData() {
    try {
        await connectDB();
        console.log('🔧 Fixing product data format...\n');

        // Get all products
        const products = await executeQuery('SELECT id, colors, sizes FROM products');
        
        console.log(`Found ${products.length} products to fix`);

        for (const product of products) {
            let fixedColors = '[]';
            let fixedSizes = '[]';

            // Fix colors
            if (product.colors && typeof product.colors === 'string') {
                if (product.colors.includes(',')) {
                    // It's a comma-separated string
                    const colorsArray = product.colors.split(',').map(c => c.trim());
                    fixedColors = JSON.stringify(colorsArray);
                } else if (product.colors.startsWith('[')) {
                    // It's already JSON
                    fixedColors = product.colors;
                } else {
                    // Single color
                    fixedColors = JSON.stringify([product.colors]);
                }
            }

            // Fix sizes
            if (product.sizes && typeof product.sizes === 'string') {
                if (product.sizes.includes(',')) {
                    // It's a comma-separated string
                    const sizesArray = product.sizes.split(',').map(s => s.trim());
                    fixedSizes = JSON.stringify(sizesArray);
                } else if (product.sizes.startsWith('[')) {
                    // It's already JSON
                    fixedSizes = product.sizes;
                } else {
                    // Single size
                    fixedSizes = JSON.stringify([product.sizes]);
                }
            }

            // Update the product
            await executeQuery(
                'UPDATE products SET colors = ?, sizes = ? WHERE id = ?',
                [fixedColors, fixedSizes, product.id]
            );

            console.log(`✅ Fixed product ${product.id}: colors=${fixedColors}, sizes=${fixedSizes}`);
        }

        console.log('\n🎉 All products fixed!');
        
        // Verify the fix
        console.log('\n🔍 Verifying fixes...');
        const verifyProducts = await executeQuery('SELECT id, name, colors, sizes FROM products LIMIT 3');
        verifyProducts.forEach(product => {
            console.log(`  ${product.name}:`);
            console.log(`    Colors: ${product.colors}`);
            console.log(`    Sizes: ${product.sizes}`);
            
            // Test JSON parsing
            try {
                const colors = JSON.parse(product.colors);
                const sizes = JSON.parse(product.sizes);
                console.log(`    ✅ JSON parsing successful: ${colors.length} colors, ${sizes.length} sizes`);
            } catch (e) {
                console.log(`    ❌ JSON parsing failed: ${e.message}`);
            }
            console.log('');
        });

        await closeConnection();
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

fixProductData();
