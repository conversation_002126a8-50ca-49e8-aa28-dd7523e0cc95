const { executeQuery, connectDB, closeConnection } = require('./config/database');
const bcrypt = require('bcryptjs');

async function checkAdminUser() {
    try {
        await connectDB();
        console.log('🔍 Checking admin user in database...');

        // Check if admin user exists
        const adminUsers = await executeQuery(
            'SELECT id, email, first_name, last_name, role, password FROM users WHERE email = ?',
            ['<EMAIL>']
        );

        if (adminUsers.length === 0) {
            console.log('❌ Admin user not found. Creating admin user...');
            
            // Hash password
            const hashedPassword = await bcrypt.hash('admin123', 12);
            
            // Create admin user
            await executeQuery(`
                INSERT INTO users (
                    email, 
                    password, 
                    first_name, 
                    last_name, 
                    role, 
                    is_verified
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                '<EMAIL>',
                hashedPassword,
                'Admin',
                'User',
                'admin',
                true
            ]);
            
            console.log('✅ Admin user created successfully');
        } else {
            const admin = adminUsers[0];
            console.log('✅ Admin user found:');
            console.log(`   ID: ${admin.id}`);
            console.log(`   Email: ${admin.email}`);
            console.log(`   Name: ${admin.first_name} ${admin.last_name}`);
            console.log(`   Role: ${admin.role}`);
            
            // Test password
            const isValidPassword = await bcrypt.compare('admin123', admin.password);
            console.log(`   Password Valid: ${isValidPassword ? '✅' : '❌'}`);
            
            if (!isValidPassword) {
                console.log('🔧 Updating admin password...');
                const newHashedPassword = await bcrypt.hash('admin123', 12);
                await executeQuery(
                    'UPDATE users SET password = ? WHERE email = ?',
                    [newHashedPassword, '<EMAIL>']
                );
                console.log('✅ Admin password updated');
            }
        }

        // Test all admin users
        const allAdmins = await executeQuery(
            'SELECT id, email, first_name, last_name, role FROM users WHERE role IN (?, ?)',
            ['admin', 'super_admin']
        );

        console.log('\n👥 All admin users:');
        allAdmins.forEach(user => {
            console.log(`   ${user.email} - ${user.role} (ID: ${user.id})`);
        });

        console.log('\n🎯 Admin user check completed!');
        console.log('\n📋 Login Credentials:');
        console.log('   Email: <EMAIL>');
        console.log('   Password: admin123');

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await closeConnection();
    }
}

checkAdminUser();
