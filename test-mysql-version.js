const { executeQuery, connectDB, closeConnection } = require('./config/database');

async function testMySQLVersion() {
  try {
    await connectDB();
    console.log('🔍 Testing MySQL Version and LIMIT Support');
    console.log('==========================================');

    // Check MySQL version
    const versionResult = await executeQuery('SELECT VERSION() as version', []);
    console.log('MySQL Version:', versionResult[0].version);

    // Test different LIMIT syntaxes
    console.log('\n1. Testing LIMIT with literal values...');
    const literal = await executeQuery('SELECT id, name FROM products WHERE is_active = 1 LIMIT 2 OFFSET 0', []);
    console.log('Literal LIMIT result count:', literal.length);

    console.log('\n2. Testing LIMIT with only one parameter...');
    try {
      const oneParam = await executeQuery('SELECT id, name FROM products WHERE is_active = 1 LIMIT ?', [2]);
      console.log('One parameter LIMIT result count:', oneParam.length);
    } catch (error) {
      console.log('One parameter LIMIT failed:', error.message);
    }

    console.log('\n3. Testing alternative LIMIT syntax...');
    try {
      const altSyntax = await executeQuery('SELECT id, name FROM products WHERE is_active = 1 LIMIT ?, ?', [0, 2]);
      console.log('Alternative LIMIT syntax result count:', altSyntax.length);
    } catch (error) {
      console.log('Alternative LIMIT syntax failed:', error.message);
    }

    console.log('\n✅ MySQL version test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await closeConnection();
  }
}

testMySQLVersion();
